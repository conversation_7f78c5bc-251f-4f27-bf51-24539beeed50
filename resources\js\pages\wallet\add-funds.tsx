import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { ArrowLeft, CreditCard, DollarSign, Shield } from 'lucide-react';
import { FormEvent, useState } from 'react';

interface Props {
    paystack_public_key: string;
}

interface SharedData {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            wallet_balance: number;
        };
    };
}

const breadcrumbs = [
    { title: 'Wallet', href: '/wallet/balance' },
    { title: 'Add Funds', href: '/wallet/add-funds' },
];

export default function AddFunds({ paystack_public_key }: Props) {
    const { auth } = usePage<SharedData>().props;
    const [isProcessing, setIsProcessing] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        amount: '',
        email: auth.user.email,
    });

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();

        if (processing || isProcessing) return;

        setIsProcessing(true);

        try {
            // Initialize payment with backend
            const response = await fetch(route('wallet.deposit.initialize'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify(data),
            });

            const result = await response.json();

            if (result.status === 'success') {
                // Redirect to Paystack payment page
                window.location.href = result.data.authorization_url;
            } else {
                alert('Payment initialization failed. Please try again.');
                setIsProcessing(false);
            }
        } catch (error) {
            console.error('Payment error:', error);
            alert('An error occurred. Please try again.');
            setIsProcessing(false);
        }
    };

    const quickAmounts = [10, 25, 50, 100, 250, 500];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Add Funds" />

            <div className="mx-auto max-w-2xl space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" asChild>
                        <a href="/wallet/balance">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Wallet
                        </a>
                    </Button>
                </div>

                <div className="space-y-2">
                    <h1 className="text-3xl font-bold">Add Funds</h1>
                    <p className="text-muted-foreground">Add money to your wallet to bid on projects and make payments</p>
                </div>

                {/* Current Balance */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Current Balance</p>
                                <p className="text-2xl font-bold">₵{Number(auth.user.wallet_balance || 0).toFixed(2)}</p>
                            </div>
                            <div className="rounded-full bg-primary/10 p-3">
                                <DollarSign className="h-6 w-6 text-primary" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Add Funds Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5" />
                            Add Funds
                        </CardTitle>
                        <CardDescription>Choose an amount to add to your wallet. Payments are processed securely through Paystack.</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Quick Amount Selection */}
                            <div className="space-y-3">
                                <Label>Quick Select</Label>
                                <div className="grid grid-cols-3 gap-3">
                                    {quickAmounts.map((amount) => (
                                        <Button
                                            key={amount}
                                            type="button"
                                            variant={data.amount === amount.toString() ? 'default' : 'outline'}
                                            onClick={() => setData('amount', amount.toString())}
                                            className="h-12"
                                        >
                                            ₵{amount}
                                        </Button>
                                    ))}
                                </div>
                            </div>

                            <Separator />

                            {/* Custom Amount */}
                            <div className="space-y-2">
                                <Label htmlFor="amount">Custom Amount</Label>
                                <div className="relative">
                                    <span className="absolute top-1/2 left-3 -translate-y-1/2 text-muted-foreground">₵</span>
                                    <Input
                                        id="amount"
                                        type="number"
                                        step="0.01"
                                        min="1"
                                        max="1000000"
                                        placeholder="0.00"
                                        value={data.amount}
                                        onChange={(e) => setData('amount', e.target.value)}
                                        className="pl-8"
                                        required
                                    />
                                </div>
                                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
                            </div>

                            {/* Email */}
                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <Input id="email" type="email" value={data.email} onChange={(e) => setData('email', e.target.value)} required />
                                {errors.email && <p className="text-sm text-destructive">{errors.email}</p>}
                            </div>

                            {/* Security Notice */}
                            <div className="rounded-lg bg-muted p-4">
                                <div className="flex items-start gap-3">
                                    <Shield className="mt-0.5 h-5 w-5 text-primary" />
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">Secure Payment</p>
                                        <p className="text-xs text-muted-foreground">
                                            Your payment is processed securely through Paystack. We never store your card details.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                className="w-full"
                                size="lg"
                                disabled={processing || isProcessing || !data.amount || parseFloat(data.amount) < 1}
                            >
                                {processing || isProcessing ? (
                                    <>Processing...</>
                                ) : (
                                    <>
                                        <CreditCard className="mr-2 h-4 w-4" />
                                        Add ₵{data.amount || '0.00'} to Wallet
                                    </>
                                )}
                            </Button>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
