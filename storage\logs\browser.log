[2025-08-24 04:16:29] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:28.360Z"} 
[2025-08-24 04:16:30] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:29.831Z"} 
[2025-08-24 04:16:51] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-24T04:16:50.874Z"} 
[2025-08-29 12:32:28] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:32:22.635Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/password.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/appearance.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:36:54] local.ERROR: [vite] Failed to reload /resources/js/pages/settings/profile.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:36:47.573Z"} 
[2025-08-29 12:37:04] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.474Z"} 
[2025-08-29 12:37:04] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.482Z"} 
[2025-08-29 12:37:04] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useState') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440 930 35 TypeError Cannot read properties of null (reading 'useState') TypeError: Cannot read properties of null (reading 'useState')
    at exports.useState (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440:930:35)
    at NavigationMenu (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-navigation-menu.js?v=b1f86e35:75:55)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10711:13) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.483Z"} 
[2025-08-29 12:37:04] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useState') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440 930 35 TypeError Cannot read properties of null (reading 'useState') TypeError: Cannot read properties of null (reading 'useState')
    at exports.useState (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=b234a440:930:35)
    at NavigationMenu (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-navigation-menu.js?v=b1f86e35:75:55)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=d332ee2b:10711:13) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.484Z"} 
[2025-08-29 12:37:04] local.WARNING: %s

%s An error occurred in the <NavigationMenu> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T12:37:03.484Z"} 
[2025-08-29 13:13:03] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T13:06:12.335Z"} 
[2025-08-29 14:02:26] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T13:19:21.068Z"} 
[2025-08-29 18:02:07] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:06.095Z"} 
[2025-08-29 18:02:56] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.447Z"} 
[2025-08-29 18:02:56] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.479Z"} 
[2025-08-29 18:02:56] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useMemo') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f 918 35 TypeError Cannot read properties of null (reading 'useMemo') TypeError: Cannot read properties of null (reading 'useMemo')
    at exports.useMemo (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f:918:35)
    at useScope (http://[::1]:5173/node_modules/.vite/deps/chunk-RHOHTIHH.js?v=6c62076f:58:20)
    at Select (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=1d933fd6:104:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10728:43) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.484Z"} 
[2025-08-29 18:02:56] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useMemo') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f 918 35 TypeError Cannot read properties of null (reading 'useMemo') TypeError: Cannot read properties of null (reading 'useMemo')
    at exports.useMemo (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=6c62076f:918:35)
    at useScope (http://[::1]:5173/node_modules/.vite/deps/chunk-RHOHTIHH.js?v=6c62076f:58:20)
    at Select (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=1d933fd6:104:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b234a440:10728:43) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.485Z"} 
[2025-08-29 18:02:56] local.WARNING: %s

%s An error occurred in the <Select> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:55.485Z"} 
[2025-08-29 18:02:57] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/dashboard.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/dashboard.tsx {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:02:57.291Z"} 
[2025-08-29 18:05:06] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.123Z"} 
[2025-08-29 18:05:06] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.124Z"} 
[2025-08-29 18:05:06] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:06.125Z"} 
[2025-08-29 18:05:19] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.794Z"} 
[2025-08-29 18:05:19] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.794Z"} 
[2025-08-29 18:05:19] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:18.795Z"} 
[2025-08-29 18:05:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.959Z"} 
[2025-08-29 18:05:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.959Z"} 
[2025-08-29 18:05:42] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:41.960Z"} 
[2025-08-29 18:05:51] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.262Z"} 
[2025-08-29 18:05:51] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.262Z"} 
[2025-08-29 18:05:51] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:05:50.263Z"} 
[2025-08-29 18:06:38] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.443Z"} 
[2025-08-29 18:06:38] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.444Z"} 
[2025-08-29 18:06:38] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:06:37.445Z"} 
[2025-08-29 18:10:05] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:04.661Z"} 
[2025-08-29 18:10:21] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.375Z"} 
[2025-08-29 18:10:21] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.375Z"} 
[2025-08-29 18:10:21] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:10:21.376Z"} 
[2025-08-29 18:17:10] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.713Z"} 
[2025-08-29 18:17:10] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.715Z"} 
[2025-08-29 18:17:10] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:09.716Z"} 
[2025-08-29 18:17:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.199Z"} 
[2025-08-29 18:17:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=6c62076f:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.199Z"} 
[2025-08-29 18:17:23] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:22.200Z"} 
[2025-08-29 18:17:34] local.ERROR: Unhandled Promise Rejection ReferenceError Toaster is not defined ReferenceError: Toaster is not defined
    at setup (http://[::1]:5173/resources/js/app.tsx:20:32)
    at http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=6c62076f:14357:12
    at async createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=6c62076f:14352:20) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:17:33.620Z"} 
[2025-08-29 18:19:10] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:19:09.870Z"} 
[2025-08-29 18:41:41] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.752Z"} 
[2025-08-29 18:41:41] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.752Z"} 
[2025-08-29 18:41:41] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:40.753Z"} 
[2025-08-29 18:41:48] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:41:48] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:41:48] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:41:47.547Z"} 
[2025-08-29 18:42:12] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.711Z"} 
[2025-08-29 18:42:12] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.711Z"} 
[2025-08-29 18:42:12] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:42:11.713Z"} 
[2025-08-29 18:45:43] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.328Z"} 
[2025-08-29 18:45:43] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7a051031:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.328Z"} 
[2025-08-29 18:45:43] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:43.329Z"} 
[2025-08-29 18:45:58] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:45:56.945Z"} 
[2025-08-29 18:46:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.905Z"} 
[2025-08-29 18:46:42] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.905Z"} 
[2025-08-29 18:46:42] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:41.906Z"} 
[2025-08-29 18:46:53] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:46:53.120Z"} 
[2025-08-29 18:47:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.236Z"} 
[2025-08-29 18:47:23] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5173/resources/js/components/project-card.tsx 40 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5173/resources/js/components/project-card.tsx:40:37)
    at ProjectCard (http://[::1]:5173/resources/js/components/project-card.tsx:114:80)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.237Z"} 
[2025-08-29 18:47:23] local.WARNING: %s

%s An error occurred in the <ProjectCard> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:47:23.237Z"} 
[2025-08-29 18:50:24] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.772Z"} 
[2025-08-29 18:50:24] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.773Z"} 
[2025-08-29 18:50:24] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:23.774Z"} 
[2025-08-29 18:50:57] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.763Z"} 
[2025-08-29 18:50:57] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.763Z"} 
[2025-08-29 18:50:57] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:50:56.764Z"} 
[2025-08-29 18:51:16] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/projects/edit.tsx Error: Page not found: ./pages/projects/edit.tsx
    at resolvePageComponent (http://[::1]:5174/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=0ee6d8bb:12:9)
    at resolve (http://[::1]:5174/resources/js/app.tsx:11:22)
    at CurrentPage.resolveComponent (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:14350:54)
    at CurrentPage.resolve (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12072:33)
    at CurrentPage.set (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12002:17)
    at _Response.setPage (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:13053:17)
    at async _Response.process (http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:12980:5) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:15.619Z"} 
[2025-08-29 18:51:26] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:51:26] local.ERROR: Uncaught TypeError: project.budget_min.toFixed is not a function http://[::1]:5174/resources/js/pages/projects/show.tsx 64 37 TypeError project.budget_min.toFixed is not a function TypeError: project.budget_min.toFixed is not a function
    at formatBudget (http://[::1]:5174/resources/js/pages/projects/show.tsx:64:37)
    at ShowProject (http://[::1]:5174/resources/js/pages/projects/show.tsx:159:83)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13) {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:51:26] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:51:26.339Z"} 
[2025-08-29 18:52:05] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.995Z"} 
[2025-08-29 18:52:05] local.ERROR: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb 871 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at SelectItem (http://[::1]:5174/node_modules/.vite/deps/@radix-ui_react-select.js?v=0ee6d8bb:871:13)
    at Object.react_stack_bottom_frame (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateForwardRef (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6461:21)
    at beginWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7864:20)
    at runWithFiberInDEV (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5174/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.995Z"} 
[2025-08-29 18:52:05] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T18:52:04.996Z"} 
[2025-08-29 19:14:37] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:14:36.766Z"} 
[2025-08-29 19:14:51] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:14:50.786Z"} 
[2025-08-29 19:15:03] local.ERROR: [vite] Failed to reload /resources/js/components/project-card.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:15:02.100Z"} 
[2025-08-29 19:31:19] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:31:18.919Z"} 
[2025-08-29 19:31:40] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:31:39.019Z"} 
[2025-08-29 19:37:28] local.ERROR: [vite] Failed to reload /resources/js/components/google-button.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:37:27.805Z"} 
[2025-08-29 19:40:33] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:40:33.461Z"} 
[2025-08-29 19:40:48] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:40:47.921Z"} 
[2025-08-29 19:48:02] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:00.655Z"} 
[2025-08-29 19:48:13] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:12.032Z"} 
[2025-08-29 19:48:16] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:15.421Z"} 
[2025-08-29 19:48:48] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:48:47.437Z"} 
[2025-08-29 19:49:02] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:49:02.200Z"} 
[2025-08-29 19:50:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:50:53.806Z"} 
[2025-08-29 19:52:21] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:52:21.721Z"} 
[2025-08-29 19:54:05] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:54:04.943Z"} 
[2025-08-29 19:57:13] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-29T19:54:51.410Z"} 
[2025-08-30 11:49:08] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:5493:15)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=0ee6d8bb:6021:41) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T11:49:07.922Z"} 
[2025-08-30 12:01:26] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:01:25.905Z"} 
[2025-08-30 12:03:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login?id=614f0b2d-175d-4270-9641-f123e0c2253f&vscodeBrowserReqId=1756555397049","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.103.2 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36","timestamp":"2025-08-30T12:03:53.381Z"} 
[2025-08-30 12:03:54] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:03:53.412Z"} 
[2025-08-30 12:05:36] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login?id=614f0b2d-175d-4270-9641-f123e0c2253f&vscodeBrowserReqId=1756555517096","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.103.2 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36","timestamp":"2025-08-30T12:05:35.423Z"} 
[2025-08-30 12:05:37] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:05:35.546Z"} 
[2025-08-30 12:36:14] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:36:13.224Z"} 
[2025-08-30 12:43:44] local.ERROR: [vite] Failed to reload /resources/css/app.css. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T12:43:43.015Z"} 
[2025-08-30 13:11:28] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.973Z"} 
[2025-08-30 13:11:28] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.973Z"} 
[2025-08-30 13:11:28] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:27.974Z"} 
[2025-08-30 13:11:38] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:38] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:38] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:37.704Z"} 
[2025-08-30 13:11:47] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:11:47] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:11:47] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:11:46.986Z"} 
[2025-08-30 13:12:42] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.089Z"} 
[2025-08-30 13:12:42] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.089Z"} 
[2025-08-30 13:12:42] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:12:42.090Z"} 
[2025-08-30 13:15:44] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.414Z"} 
[2025-08-30 13:15:44] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.414Z"} 
[2025-08-30 13:15:44] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse?page=2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:15:44.416Z"} 
[2025-08-30 13:21:16] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.436Z"} 
[2025-08-30 13:21:16] local.ERROR: Uncaught TypeError: user.wallet_balance?.toFixed is not a function http://[::1]:5173/resources/js/components/user-menu-content.tsx 155 32 TypeError user.wallet_balance?.toFixed is not a function TypeError: user.wallet_balance?.toFixed is not a function
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:155:32)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.436Z"} 
[2025-08-30 13:21:16] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:21:16.440Z"} 
[2025-08-30 13:48:16] local.DEBUG: Project data: null {"url":"http://localhost:8000/projects/e-commerce-website-development-JvEEUI","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:48:15.783Z"} 
[2025-08-30 13:58:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/e-commerce-website-development-JvEEUI","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:58:45.725Z"} 
[2025-08-30 13:58:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T13:58:45.726Z"} 
[2025-08-30 14:01:32] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.508Z"} 
[2025-08-30 14:01:32] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.508Z"} 
[2025-08-30 14:01:32] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:31.511Z"} 
[2025-08-30 14:01:41] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.521Z"} 
[2025-08-30 14:01:41] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.521Z"} 
[2025-08-30 14:01:41] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:01:40.522Z"} 
[2025-08-30 14:18:20] local.ERROR: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.744Z"} 
[2025-08-30 14:18:20] local.ERROR: Uncaught ReferenceError: Link is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500 811 15 ReferenceError Link is not defined ReferenceError: Link is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756563474500:811:15)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0ee6d8bb:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.744Z"} 
[2025-08-30 14:18:20] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:19.747Z"} 
[2025-08-30 14:18:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:18:54.664Z"} 
[2025-08-30 14:21:38] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563535210 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563535210 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:37.962Z"} 
[2025-08-30 14:21:40] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563699022 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563699022 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:40.142Z"} 
[2025-08-30 14:21:42] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563700874 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563700874 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:42.122Z"} 
[2025-08-30 14:21:44] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563702904 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563702904 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:44.195Z"} 
[2025-08-30 14:21:46] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563705010 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563705010 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:45.744Z"} 
[2025-08-30 14:21:48] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563706578 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563706578 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:47.669Z"} 
[2025-08-30 14:21:50] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563708455 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563708455 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:49.739Z"} 
[2025-08-30 14:21:52] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563710601 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563710601 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:51.813Z"} 
[2025-08-30 14:21:54] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563712572 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563712572 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:53.673Z"} 
[2025-08-30 14:21:56] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563714486 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563714486 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:55.580Z"} 
[2025-08-30 14:21:58] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563716549 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563716549 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:57.508Z"} 
[2025-08-30 14:22:00] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563718329 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563718329 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:21:59.615Z"} 
[2025-08-30 14:22:02] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563720655 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563720655 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:01.350Z"} 
[2025-08-30 14:22:04] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563722231 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563722231 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:03.262Z"} 
[2025-08-30 14:22:06] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563724068 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563724068 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:05.486Z"} 
[2025-08-30 14:22:09] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563726153 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563726153 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:08.296Z"} 
[2025-08-30 14:22:13] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563729743 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563729743 {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:12.199Z"} 
[2025-08-30 14:22:40] local.ERROR: Uncaught TypeError: project.accepted_bid_amount.toFixed is not a function http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447 264 49 TypeError project.accepted_bid_amount.toFixed is not a function TypeError: project.accepted_bid_amount.toFixed is not a function
    at Milestones (http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447:264:49)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.370Z"} 
[2025-08-30 14:22:40] local.ERROR: Uncaught TypeError: project.accepted_bid_amount.toFixed is not a function http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447 264 49 TypeError project.accepted_bid_amount.toFixed is not a function TypeError: project.accepted_bid_amount.toFixed is not a function
    at Milestones (http://[::1]:5173/resources/js/pages/projects/milestones.tsx?t=1756563732447:264:49)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.370Z"} 
[2025-08-30 14:22:40] local.WARNING: %s

%s An error occurred in the <Milestones> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/e-commerce-website-development-YEOOQW/milestones","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T14:22:40.373Z"} 
[2025-08-30 15:26:31] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.284Z"} 
[2025-08-30 15:26:31] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.285Z"} 
[2025-08-30 15:26:31] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:31.285Z"} 
[2025-08-30 15:26:35] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.616Z"} 
[2025-08-30 15:26:35] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.616Z"} 
[2025-08-30 15:26:35] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:26:35.617Z"} 
[2025-08-30 15:43:17] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:43:16.985Z"} 
[2025-08-30 15:43:17] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:43:16.986Z"} 
[2025-08-30 15:43:17] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:43:16.986Z"} 
[2025-08-30 15:44:53] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:44:51.288Z"} 
[2025-08-30 15:44:53] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:44:51.289Z"} 
[2025-08-30 15:44:53] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:44:51.290Z"} 
[2025-08-30 15:46:11] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:08.441Z"} 
[2025-08-30 15:46:11] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:08.442Z"} 
[2025-08-30 15:46:11] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:08.444Z"} 
[2025-08-30 15:46:59] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:58.839Z"} 
[2025-08-30 15:46:59] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:58.839Z"} 
[2025-08-30 15:46:59] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:46:58.840Z"} 
[2025-08-30 15:48:26] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:25.125Z"} 
[2025-08-30 15:48:26] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:25.126Z"} 
[2025-08-30 15:48:26] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:25.126Z"} 
[2025-08-30 15:48:42] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:41.045Z"} 
[2025-08-30 15:48:42] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:41.045Z"} 
[2025-08-30 15:48:42] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:48:41.046Z"} 
[2025-08-30 15:50:47] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:50:47.168Z"} 
[2025-08-30 15:50:47] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:50:47.168Z"} 
[2025-08-30 15:50:47] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:50:47.168Z"} 
[2025-08-30 15:51:05] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:51:04.083Z"} 
[2025-08-30 15:51:05] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:51:04.083Z"} 
[2025-08-30 15:51:05] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:51:04.083Z"} 
[2025-08-30 15:52:56] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:52:55.101Z"} 
[2025-08-30 15:52:56] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:52:55.102Z"} 
[2025-08-30 15:52:56] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:52:55.102Z"} 
[2025-08-30 15:53:29] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:28.065Z"} 
[2025-08-30 15:53:29] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:28.067Z"} 
[2025-08-30 15:53:29] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:28.068Z"} 
[2025-08-30 15:53:47] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:46.051Z"} 
[2025-08-30 15:53:47] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756568920455:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:46.055Z"} 
[2025-08-30 15:53:47] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:53:46.056Z"} 
[2025-08-30 15:54:40] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:54:40.528Z"} 
[2025-08-30 15:54:40] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756569225244:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:54:40.528Z"} 
[2025-08-30 15:54:40] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:54:40.529Z"} 
[2025-08-30 15:55:16] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:55:15.438Z"} 
[2025-08-30 15:55:45] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:55:45.389Z"} 
[2025-08-30 15:55:45] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:55:45.389Z"} 
[2025-08-30 15:55:45] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T15:55:45.390Z"} 
[2025-08-30 16:11:11] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:10.664Z"} 
[2025-08-30 16:11:11] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570267298:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:10.664Z"} 
[2025-08-30 16:11:11] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:10.665Z"} 
[2025-08-30 16:11:21] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:19.512Z"} 
[2025-08-30 16:11:21] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:19.513Z"} 
[2025-08-30 16:11:21] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:19.514Z"} 
[2025-08-30 16:11:52] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:51.812Z"} 
[2025-08-30 16:11:52] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:51.812Z"} 
[2025-08-30 16:11:52] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:11:51.813Z"} 
[2025-08-30 16:14:18] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:14:18.418Z"} 
[2025-08-30 16:14:18] local.ERROR: Uncaught TypeError: user.wallet_balance.toFixed is not a function http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988 687 39 TypeError user.wallet_balance.toFixed is not a function TypeError: user.wallet_balance.toFixed is not a function
    at http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:687:39
    at Array.map (<anonymous>)
    at UserManagement (http://[::1]:5173/resources/js/pages/Admin/UserManagement.tsx?t=1756570275988:627:116)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:14:18.418Z"} 
[2025-08-30 16:14:18] local.WARNING: %s

%s An error occurred in the <UserManagement> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:14:18.420Z"} 
[2025-08-30 16:18:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570276042 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570276042 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:18:54.418Z"} 
[2025-08-30 16:19:04] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570735080 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570735080 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:19:03.209Z"} 
[2025-08-30 16:19:15] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570744095 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570744095 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:19:15.404Z"} 
[2025-08-30 16:19:19] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570756033 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570756033 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:19:19.347Z"} 
[2025-08-30 16:19:26] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570760091 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570760091 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:19:25.375Z"} 
[2025-08-30 16:19:31] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570766368 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570766368 {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:19:30.441Z"} 
[2025-08-30 16:20:01] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570770659 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570770659 {"url":"http://localhost:8000/funds/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:00.442Z"} 
[2025-08-30 16:20:20] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:19.539Z"} 
[2025-08-30 16:20:20] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:19.539Z"} 
[2025-08-30 16:20:20] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:19.540Z"} 
[2025-08-30 16:20:28] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:28.098Z"} 
[2025-08-30 16:20:28] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:28.098Z"} 
[2025-08-30 16:20:28] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:20:28.099Z"} 
[2025-08-30 16:21:30] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:21:29.910Z"} 
[2025-08-30 16:21:30] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:21:29.911Z"} 
[2025-08-30 16:21:30] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:21:29.911Z"} 
[2025-08-30 16:22:42] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570801240 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570801240 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:42.443Z"} 
[2025-08-30 16:22:44] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570962832 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570962832 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:43.942Z"} 
[2025-08-30 16:22:46] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570964010 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570964010 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:45.212Z"} 
[2025-08-30 16:22:47] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570965181 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570965181 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:46.389Z"} 
[2025-08-30 16:22:48] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570966529 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570966529 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:48.081Z"} 
[2025-08-30 16:22:51] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570968951 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570968951 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:50.105Z"} 
[2025-08-30 16:22:52] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570970206 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570970206 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:51.908Z"} 
[2025-08-30 16:22:53] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570972020 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570972020 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:53.083Z"} 
[2025-08-30 16:22:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570973710 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570973710 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:54.957Z"} 
[2025-08-30 16:22:58] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570975835 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570975835 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:57.266Z"} 
[2025-08-30 16:22:59] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570977191 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570977191 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:58.559Z"} 
[2025-08-30 16:23:00] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570978643 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570978643 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:22:59.690Z"} 
[2025-08-30 16:23:02] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570980332 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570980332 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:01.423Z"} 
[2025-08-30 16:23:05] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570982218 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570982218 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:04.687Z"} 
[2025-08-30 16:23:08] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570985186 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570985186 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:06.895Z"} 
[2025-08-30 16:23:09] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570987731 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570987731 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:08.903Z"} 
[2025-08-30 16:23:11] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570989570 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570989570 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:10.725Z"} 
[2025-08-30 16:23:12] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570990970 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570990970 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:12.107Z"} 
[2025-08-30 16:23:14] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570992568 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570992568 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:13.588Z"} 
[2025-08-30 16:23:16] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570993881 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570993881 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:15.567Z"} 
[2025-08-30 16:23:17] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570996051 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570996051 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:16.524Z"} 
[2025-08-30 16:23:18] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570996626 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570996626 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:17.964Z"} 
[2025-08-30 16:23:20] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570998743 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756570998743 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:19.513Z"} 
[2025-08-30 16:23:22] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571000200 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571000200 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:21.429Z"} 
[2025-08-30 16:23:24] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571001565 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571001565 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:22.770Z"} 
[2025-08-30 16:23:25] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571004086 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571004086 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:25.196Z"} 
[2025-08-30 16:23:27] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571005428 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571005428 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:26.704Z"} 
[2025-08-30 16:23:29] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571007188 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571007188 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:28.530Z"} 
[2025-08-30 16:23:31] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571008411 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571008411 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:29.970Z"} 
[2025-08-30 16:23:33] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571010058 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/MyProjects.tsx?t=1756571010058 {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:32.296Z"} 
[2025-08-30 16:23:39] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:38.544Z"} 
[2025-08-30 16:23:39] local.ERROR: Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem. {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:38.595Z"} 
[2025-08-30 16:23:39] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useContext') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=3c00be01 880 27 TypeError Cannot read properties of null (reading 'useContext') TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=3c00be01:880:27)
    at useDirection (http://[::1]:5173/node_modules/.vite/deps/chunk-4ZHYBG2R.js?v=3c00be01:16:27)
    at Tabs (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=14678dbc:64:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:38.598Z"} 
[2025-08-30 16:23:39] local.ERROR: Uncaught TypeError: Cannot read properties of null (reading 'useContext') http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=3c00be01 880 27 TypeError Cannot read properties of null (reading 'useContext') TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (http://[::1]:5173/node_modules/.vite/deps/chunk-575JY5N6.js?v=3c00be01:880:27)
    at useDirection (http://[::1]:5173/node_modules/.vite/deps/chunk-4ZHYBG2R.js?v=3c00be01:16:27)
    at Tabs (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=14678dbc:64:23)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:4206:24)
    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:6461:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:7864:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9ba78d2c:10728:43) {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:38.598Z"} 
[2025-08-30 16:23:39] local.WARNING: %s

%s An error occurred in the <Tabs> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:23:38.599Z"} 
[2025-08-30 16:25:44] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:25:43.768Z"} 
[2025-08-30 16:25:44] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756570275982:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:25:43.769Z"} 
[2025-08-30 16:25:44] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:25:43.770Z"} 
[2025-08-30 16:28:08] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:28:08.045Z"} 
[2025-08-30 16:28:09] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:28:08.044Z"} 
[2025-08-30 16:28:34] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:28:34.223Z"} 
[2025-08-30 16:28:34] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:28:34.223Z"} 
[2025-08-30 16:28:34] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:28:34.224Z"} 
[2025-08-30 16:29:42] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:29:42.107Z"} 
[2025-08-30 16:29:42] local.ERROR: Uncaught ReferenceError: Dialog is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx 913 28 ReferenceError Dialog is not defined ReferenceError: Dialog is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx:913:28)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:29:42.107Z"} 
[2025-08-30 16:29:42] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:29:42.110Z"} 
[2025-08-30 16:30:18] local.ERROR: Uncaught ReferenceError: AlertTriangle is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756571417666 917 34 ReferenceError AlertTriangle is not defined ReferenceError: AlertTriangle is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756571417666:917:34)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:30:18.122Z"} 
[2025-08-30 16:30:18] local.ERROR: Uncaught ReferenceError: AlertTriangle is not defined http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756571417666 917 34 ReferenceError AlertTriangle is not defined ReferenceError: AlertTriangle is not defined
    at ShowProject (http://[::1]:5173/resources/js/pages/projects/show.tsx?t=1756571417666:917:34)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=3c00be01:10359:46) {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:30:18.122Z"} 
[2025-08-30 16:30:18] local.WARNING: %s

%s An error occurred in the <ShowProject> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/projects/mobile-app-for-campus-event-management-7uzdZc","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T16:30:18.125Z"} 
[2025-08-30 17:04:30] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/Wallet/Transactions.tsx Error: Page not found: ./pages/Wallet/Transactions.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=2f585d1d:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:11:22)
    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=2f585d1d:11832:54)
    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=2f585d1d:9554:33)
    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=2f585d1d:9484:17)
    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=2f585d1d:10535:17)
    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=2f585d1d:10462:5) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:04:29.906Z"} 
[2025-08-30 17:05:27] local.ERROR: In HTML, %s cannot be a descendant of <%s>.
This will cause a hydration error.%s <ul> p ...
    <FocusScope asChild={true} loop={true} trapped={true} onMountAutoFocus={undefined} ...>
      <Primitive.div tabIndex={-1} asChild={true} ref={function} onKeyDown={function}>
        <Primitive.div.Slot tabIndex={-1} onKeyDown={function} ref={function}>
          <Primitive.div.SlotClone tabIndex={-1} onKeyDown={function} ref={function}>
            <DismissableLayer role="dialog" id="radix-«r2c»" aria-describedby="radix-«r2e»" aria-labelledby="radix-«r2d»" ...>
              <Primitive.div role="dialog" id="radix-«r2c»" aria-describedby="radix-«r2e»" aria-labelledby="radix-«r2d»" ...>
                <div role="dialog" id="radix-«r2c»" aria-describedby="radix-«r2e»" aria-labelledby="radix-«r2d»" ...>
                  <DialogHeader>
                    <div data-slot="dialog-header" className="flex flex-...">
                      <DialogTitle>
                      <DialogDescription>
                        <DialogDescription data-slot="dialog-des..." className="text-muted...">
                          <Primitive.p id="radix-«r2e»" data-slot="dialog-des..." className="text-muted..." ref={null}>
>                           <p
>                             id="radix-«r2e»"
>                             data-slot="dialog-description"
>                             className="text-muted-foreground text-sm"
>                             ref={null}
>                           >
                              <strong>
                              <br>
                              <br>
                              <span>
>                             <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
                  ...
                  ...
    ... {"url":"http://localhost:8000/projects/financial-analysis-of-ghanaian-banking-sector-3cIiFy","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:05:26.733Z"} 
[2025-08-30 17:05:27] local.ERROR: <%s> cannot contain a nested %s.
See this log for the ancestor stack trace. p <ul> {"url":"http://localhost:8000/projects/financial-analysis-of-ghanaian-banking-sector-3cIiFy","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:05:26.733Z"} 
[2025-08-30 17:05:49] local.ERROR: Failed to accept bid: This project is no longer accepting bids. {"url":"http://localhost:8000/projects/financial-analysis-of-ghanaian-banking-sector-3cIiFy","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:05:48.488Z"} 
[2025-08-30 17:34:39] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=************* TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=************* {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:34:39.236Z"} 
[2025-08-30 17:34:40] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=************* TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=************* {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:34:40.428Z"} 
[2025-08-30 17:34:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575293923 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575293923 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:34:54.547Z"} 
[2025-08-30 17:36:03] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:36:02.383Z"} 
[2025-08-30 17:36:33] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:36:32.879Z"} 
[2025-08-30 17:36:49] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575361368 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:36:48.709Z"} 
[2025-08-30 17:36:55] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575414306 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575414306 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:36:55.227Z"} 
[2025-08-30 17:37:19] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575438302 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575438302 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:37:18.783Z"} 
[2025-08-30 17:38:07] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575486028 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575486028 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:38:06.814Z"} 
[2025-08-30 17:39:36] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575573450 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575573450 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:39:34.616Z"} 
[2025-08-30 17:40:12] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575610114 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575610114 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:40:11.111Z"} 
[2025-08-30 17:41:17] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575675021 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575675021 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:41:15.979Z"} 
[2025-08-30 17:42:10] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575728418 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/welcome.tsx?t=1756575728418 {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:42:09.014Z"} 
[2025-08-30 17:45:47] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:45:47.276Z"} 
[2025-08-30 17:47:04] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/register.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/register.tsx {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:47:04.324Z"} 
[2025-08-30 17:47:06] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/register.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/register.tsx {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:47:05.576Z"} 
[2025-08-30 17:47:15] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/login.tsx TypeError: Failed to fetch dynamically imported module: http://[::1]:5174/resources/js/pages/auth/login.tsx {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:47:15.107Z"} 
[2025-08-30 17:47:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T17:47:45.515Z"} 
[2025-08-30 20:50:36] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T20:49:03.913Z"} 
[2025-08-30 21:48:45] local.DEBUG: Button clicked via JavaScript {"url":"http://localhost:8000/admin/users/create","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T21:48:44.169Z"} 
[2025-08-30 22:22:42] local.ERROR: [vite] Failed to reload /resources/js/components/app-header.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse?search=Dolor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:22:40.714Z"} 
[2025-08-30 22:23:09] local.ERROR: [vite] Failed to reload /resources/js/pages/projects/browse.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse?search=Dolor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:23:08.453Z"} 
[2025-08-30 22:23:23] local.ERROR: [vite] Failed to reload /resources/js/pages/projects/browse.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse?search=Dolor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:23:23.070Z"} 
[2025-08-30 22:23:32] local.ERROR: [vite] Failed to reload /resources/js/pages/projects/browse.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse?search=Dolor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:23:31.504Z"} 
[2025-08-30 22:24:58] local.ERROR: [vite] Failed to reload /resources/js/components/app-header.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse?search=Dolor","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:24:57.416Z"} 
[2025-08-30 22:27:53] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=7660f6dd:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=7660f6dd:2143:41) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:27:50.773Z"} 
[2025-08-30 22:27:57] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:27:56.649Z"} 
[2025-08-30 22:53:33] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:53:33.083Z"} 
[2025-08-30 22:56:40] local.DEBUG: Search input value: null {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:56:39.371Z"} 
[2025-08-30 22:56:40] local.DEBUG: Apply button found: true {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:56:39.371Z"} 
[2025-08-30 22:56:52] local.DEBUG: Search input value after setting: mobile {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:56:51.247Z"} 
[2025-08-30 22:57:12] local.DEBUG: Search input value: blockchain {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T22:57:10.985Z"} 
[2025-08-30 23:15:42] local.ERROR: Uncaught ReferenceError: mockNotifications is not defined http://[::1]:5173/resources/js/components/notifications-dropdown.tsx?t=1756595741656 86 73 ReferenceError mockNotifications is not defined ReferenceError: mockNotifications is not defined
    at NotificationsDropdown (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx?t=1756595741656:86:73)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:15:42.339Z"} 
[2025-08-30 23:15:42] local.ERROR: Uncaught ReferenceError: mockNotifications is not defined http://[::1]:5173/resources/js/components/notifications-dropdown.tsx?t=1756595741656 86 73 ReferenceError mockNotifications is not defined ReferenceError: mockNotifications is not defined
    at NotificationsDropdown (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx?t=1756595741656:86:73)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:15:42.340Z"} 
[2025-08-30 23:15:42] local.WARNING: %s

%s An error occurred in the <NotificationsDropdown> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:15:42.343Z"} 
[2025-08-30 23:16:42] local.ERROR: Uncaught ReferenceError: mockMessages is not defined http://[::1]:5173/resources/js/components/messages-dropdown.tsx?t=1756595802208 91 73 ReferenceError mockMessages is not defined ReferenceError: mockMessages is not defined
    at MessagesDropdown (http://[::1]:5173/resources/js/components/messages-dropdown.tsx?t=1756595802208:91:73)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:16:42.738Z"} 
[2025-08-30 23:16:42] local.ERROR: Uncaught ReferenceError: mockMessages is not defined http://[::1]:5173/resources/js/components/messages-dropdown.tsx?t=1756595802208 91 73 ReferenceError mockMessages is not defined ReferenceError: mockMessages is not defined
    at MessagesDropdown (http://[::1]:5173/resources/js/components/messages-dropdown.tsx?t=1756595802208:91:73)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:16:42.738Z"} 
[2025-08-30 23:16:42] local.WARNING: %s

%s An error occurred in the <MessagesDropdown> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:16:42.739Z"} 
[2025-08-30 23:17:54] local.ERROR: [vite] Failed to reload /resources/js/components/chat-widget.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:17:53.977Z"} 
[2025-08-30 23:29:46] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/inbox","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-30T23:29:45.538Z"} 
[2025-08-31 06:45:52] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/Wallet/AddFunds.tsx Error: Page not found: ./pages/Wallet/AddFunds.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=7660f6dd:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:11:22)
    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:11832:54)
    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9554:33)
    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9484:17)
    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10535:17)
    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10462:5) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T06:45:51.071Z"} 
[2025-08-31 06:46:13] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/Wallet/AddFunds.tsx Error: Page not found: ./pages/Wallet/AddFunds.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=7660f6dd:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:11:22)
    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:11832:54)
    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9554:33)
    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:9484:17)
    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10535:17)
    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=7660f6dd:10462:5) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T06:46:12.304Z"} 
[2025-08-31 06:55:31] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-08-31T06:55:30.527Z"} 
[2025-08-31 07:22:08] local.WARNING: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T07:22:07.756Z"} 
[2025-08-31 07:24:12] local.WARNING: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {"url":"http://localhost:8000/inbox","user_agent":"Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-08-31T07:24:11.894Z"} 
[2025-08-31 07:33:15] local.ERROR: [vite] Failed to reload /resources/js/pages/MyProjects.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T07:33:14.832Z"} 
[2025-08-31 07:47:32] local.WARNING: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {"url":"http://localhost:8000/profile","user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1","timestamp":"2025-08-31T07:47:31.972Z"} 
[2025-08-31 08:38:29] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T08:38:28.987Z"} 
[2025-08-31 14:00:19] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:00:18.205Z"} 
[2025-08-31 14:00:19] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:00:18.205Z"} 
[2025-08-31 14:00:19] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:00:18.206Z"} 
[2025-08-31 14:06:09] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:06:08.961Z"} 
[2025-08-31 14:06:09] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:06:08.963Z"} 
[2025-08-31 14:06:09] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:06:08.965Z"} 
[2025-08-31 14:07:30] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:28.808Z"} 
[2025-08-31 14:07:30] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:28.808Z"} 
[2025-08-31 14:07:30] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:28.809Z"} 
[2025-08-31 14:07:37] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:36.122Z"} 
[2025-08-31 14:07:37] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:36.122Z"} 
[2025-08-31 14:07:37] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:36.123Z"} 
[2025-08-31 14:07:59] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:58.217Z"} 
[2025-08-31 14:07:59] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:58.217Z"} 
[2025-08-31 14:07:59] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:07:58.217Z"} 
[2025-08-31 14:08:13] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:12.217Z"} 
[2025-08-31 14:08:13] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx 165 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx:165:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:12.217Z"} 
[2025-08-31 14:08:13] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:12.217Z"} 
[2025-08-31 14:08:27] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649305888 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649305888:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:26.220Z"} 
[2025-08-31 14:08:27] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649305888 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649305888:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:26.220Z"} 
[2025-08-31 14:08:27] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:26.220Z"} 
[2025-08-31 14:08:38] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649316281 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649316281:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:37.218Z"} 
[2025-08-31 14:08:38] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649316281 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649316281:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:37.218Z"} 
[2025-08-31 14:08:38] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:37.219Z"} 
[2025-08-31 14:08:58] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649336754 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649336754:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:57.218Z"} 
[2025-08-31 14:08:58] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649336754 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649336754:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:57.218Z"} 
[2025-08-31 14:08:58] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:08:57.218Z"} 
[2025-08-31 14:10:36] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649431611 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649431611:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:10:33.540Z"} 
[2025-08-31 14:10:36] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'wallet_balance') http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649431611 136 18 TypeError Cannot read properties of undefined (reading 'wallet_balance') TypeError: Cannot read properties of undefined (reading 'wallet_balance')
    at Transactions (http://[::1]:5173/resources/js/pages/wallet/transactions.tsx?t=1756649431611:136:18)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10359:46) {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:10:33.540Z"} 
[2025-08-31 14:10:36] local.WARNING: %s

%s An error occurred in the <Transactions> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:10:33.541Z"} 
[2025-08-31 14:19:49] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/ 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/:48:19101)
    at http://localhost:8000/:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:19:49.341Z"} 
[2025-08-31 14:19:49] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/ 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/:48:19101)
    at http://localhost:8000/:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:19:49.341Z"} 
[2025-08-31 14:19:49] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:19:49.342Z"} 
[2025-08-31 14:20:07] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/dashboard:48:19101)
    at http://localhost:8000/dashboard:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:20:06.341Z"} 
[2025-08-31 14:20:07] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/dashboard:48:19101)
    at http://localhost:8000/dashboard:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:20:06.341Z"} 
[2025-08-31 14:20:07] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:20:06.341Z"} 
[2025-08-31 14:32:43] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/dashboard:48:19101)
    at http://localhost:8000/dashboard:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx?t=1756650673040:202:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:32:42.956Z"} 
[2025-08-31 14:32:43] local.ERROR: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.
    at new e (http://localhost:8000/dashboard:48:19101)
    at http://localhost:8000/dashboard:48:24811
    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx?t=1756650673040:202:139)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:32:42.956Z"} 
[2025-08-31 14:32:43] local.WARNING: %s

%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:32:42.957Z"} 
[2025-08-31 14:45:33] local.ERROR: Uncaught TypeError: d.user.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js 6 1866 TypeError d.user.wallet_balance.toFixed is not a function TypeError: d.user.wallet_balance.toFixed is not a function
    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)
    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)
    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)
    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)
    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)
    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)
    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)
    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)
    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:45:32.812Z"} 
[2025-08-31 14:45:33] local.ERROR: Uncaught TypeError: d.user.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js 6 1866 TypeError d.user.wallet_balance.toFixed is not a function TypeError: d.user.wallet_balance.toFixed is not a function
    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)
    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)
    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)
    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)
    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)
    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)
    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)
    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)
    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:45:32.813Z"} 
[2025-08-31 14:47:06] local.ERROR: Uncaught TypeError: m.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js 6 1869 TypeError m.wallet_balance.toFixed is not a function TypeError: m.wallet_balance.toFixed is not a function
    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)
    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)
    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)
    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)
    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)
    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)
    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)
    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)
    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595) {"url":"http://127.0.0.1:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:47:06.084Z"} 
[2025-08-31 14:47:06] local.ERROR: Uncaught TypeError: m.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js 6 1869 TypeError m.wallet_balance.toFixed is not a function TypeError: m.wallet_balance.toFixed is not a function
    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)
    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)
    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)
    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)
    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)
    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)
    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)
    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)
    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595) {"url":"http://127.0.0.1:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T14:47:06.084Z"} 
