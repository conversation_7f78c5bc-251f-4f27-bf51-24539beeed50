{"__meta": {"id": "01K407ZTQJQZEF9QE3TVCCQBHR", "datetime": "2025-08-31 14:20:07", "utime": **********.283744, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[14:20:07] LOG.error: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.\n    at new e (http://localhost:8000/dashboard:48:19101)\n    at http://localhost:8000/dashboard:48:24811\n    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {\n    \"url\": \"http:\\/\\/localhost:8000\\/dashboard\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:20:06.341Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.249957, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:07] LOG.error: Uncaught Error: Ziggy error: route 'wallet.transactions' is not in the route list. http://localhost:8000/dashboard 48 19101 Error Ziggy error: route 'wallet.transactions' is not in the route list. Error: Ziggy error: route 'wallet.transactions' is not in the route list.\n    at new e (http://localhost:8000/dashboard:48:19101)\n    at http://localhost:8000/dashboard:48:24811\n    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)\n    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43) {\n    \"url\": \"http:\\/\\/localhost:8000\\/dashboard\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:20:06.341Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.250672, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:07] LOG.warning: %s\n\n%s An error occurred in the <UserMenuContent> component. Consider adding an error boundary to your tree to customize error handling behavior.\nVisit https://react.dev/link/error-boundaries to learn more about error boundaries. {\n    \"url\": \"http:\\/\\/localhost:8000\\/dashboard\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:20:06.341Z\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.251192, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": 1756650006.760568, "end": **********.283797, "duration": 0.5232291221618652, "duration_str": "523ms", "measures": [{"label": "Booting", "start": 1756650006.760568, "relative_start": 0, "end": **********.187418, "relative_end": **********.187418, "duration": 0.****************, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.187456, "relative_start": 0.*****************, "end": **********.283802, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "96.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.235677, "relative_start": 0.****************, "end": **********.240744, "relative_end": **********.240744, "duration": 0.005067110061645508, "duration_str": "5.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.278999, "relative_start": 0.****************, "end": **********.279388, "relative_end": **********.279388, "duration": 0.00038886070251464844, "duration_str": "389μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.27945, "relative_start": 0.****************, "end": **********.279489, "relative_end": **********.279489, "duration": 3.910064697265625e-05, "duration_str": "39μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.27445, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "526ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2042854 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2042854\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-746231726 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:20:06.341Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Uncaught Error: Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>48</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>19101</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"66 characters\">Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1038 characters\">Error: Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at new e (http://localhost:8000/dashboard:48:19101)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at http://localhost:8000/dashboard:48:24811<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:20:06.341Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Uncaught Error: Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>48</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>19101</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"66 characters\">Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1038 characters\">Error: Ziggy error: route &#039;wallet.transactions&#039; is not in the route list.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at new e (http://localhost:8000/dashboard:48:19101)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at http://localhost:8000/dashboard:48:24811<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at UserMenuContent (http://[::1]:5173/resources/js/components/user-menu-content.tsx:170:139)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:6619:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:7654:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1038 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=7660f6dd:10728:43)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">warn</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:20:06.341Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"\"\"\n          <span class=sf-dump-str title=\"6 characters\">%s<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\">%s</span>\n          \"\"\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"53 characters\">An error occurred in the &lt;UserMenuContent&gt; component.</span>\"\n        <span class=sf-dump-index>2</span> => \"\"\"\n          <span class=sf-dump-str title=\"168 characters\">Consider adding an error boundary to your tree to customize error handling behavior.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"168 characters\">Visit https://react.dev/link/error-boundaries to learn more about error boundaries.</span>\n          \"\"\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746231726\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1968094848 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3621</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"958 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6InVBdy96VG02RGVnSkZsV1lUZGc0OHc9PSIsInZhbHVlIjoiQ1dQWkRyQXA0ZE0rbjlVb0ZnQ2tiSzlwSWJyVkZsaTVaek1JaEp2dkxxYVN4UDFLV3h3Q2Vmc0VXUXdzMk4rVzRYeUFwZVI1elg1QlBqN2NIYXdnSkswUlJnTmFNblIvWHBKczRvYTNNaUZmSjdhT3FHemd3YmNydTMvSW5jOWEiLCJtYWMiOiI1ZjliNDhjNzAxMzg3ZTA0NDA0MDM2Y2QxMjJiOTBmNzYyMTQ4ZTY4NTBmYWY2Y2RkY2M5ODJiZDliZTNlNDQ2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFQRmdFajJOQWErL2c5TlJ5dmdmS0E9PSIsInZhbHVlIjoiMWZkYUh3R3k4UjlXREw2S3JQYnhXVWt6S3NCTm1iV3VSaW0rUHZIdFZ1eVpIeU1RMUFOMDJ4YzZia3NwN05EQ21RMjJaYjNpNnN3ZnVlWG5NWTJ5UUUzRU9lRzVvZHVWMDVqVlorVEowQXhVK1ptTnM5ZGFSdjREcHo4OEhyU1UiLCJtYWMiOiI5MWRkNzI2MjE0MWY2YWVjODI5NjgyODg2ZjU4YWI4MjQ1YzcyZDEzYjhjODJhYTBiZmUxMDk4Zjc1ODFlMmYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968094848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-984652324 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InVBdy96VG02RGVnSkZsV1lUZGc0OHc9PSIsInZhbHVlIjoiQ1dQWkRyQXA0ZE0rbjlVb0ZnQ2tiSzlwSWJyVkZsaTVaek1JaEp2dkxxYVN4UDFLV3h3Q2Vmc0VXUXdzMk4rVzRYeUFwZVI1elg1QlBqN2NIYXdnSkswUlJnTmFNblIvWHBKczRvYTNNaUZmSjdhT3FHemd3YmNydTMvSW5jOWEiLCJtYWMiOiI1ZjliNDhjNzAxMzg3ZTA0NDA0MDM2Y2QxMjJiOTBmNzYyMTQ4ZTY4NTBmYWY2Y2RkY2M5ODJiZDliZTNlNDQ2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlFQRmdFajJOQWErL2c5TlJ5dmdmS0E9PSIsInZhbHVlIjoiMWZkYUh3R3k4UjlXREw2S3JQYnhXVWt6S3NCTm1iV3VSaW0rUHZIdFZ1eVpIeU1RMUFOMDJ4YzZia3NwN05EQ21RMjJaYjNpNnN3ZnVlWG5NWTJ5UUUzRU9lRzVvZHVWMDVqVlorVEowQXhVK1ptTnM5ZGFSdjREcHo4OEhyU1UiLCJtYWMiOiI5MWRkNzI2MjE0MWY2YWVjODI5NjgyODg2ZjU4YWI4MjQ1YzcyZDEzYjhjODJhYTBiZmUxMDk4Zjc1ODFlMmYxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984652324\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-131416534 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:20:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131416534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2069792775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2069792775\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}