{"__meta": {"id": "01K409H7Y9CC4N45T8FGZHA6HY", "datetime": "2025-08-31 14:47:06", "utime": **********.442215, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[14:47:06] LOG.error: Uncaught TypeError: m.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js 6 1869 TypeError m.wallet_balance.toFixed is not a function TypeError: m.wallet_balance.toFixed is not a function\n    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)\n    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)\n    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)\n    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)\n    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)\n    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)\n    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)\n    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)\n    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)\n    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595) {\n    \"url\": \"http:\\/\\/127.0.0.1:8000\\/wallet\\/transactions\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:47:06.084Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.426943, "xdebug_link": null, "collector": "log"}, {"message": "[14:47:06] LOG.error: Uncaught TypeError: m.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js 6 1869 TypeError m.wallet_balance.toFixed is not a function TypeError: m.wallet_balance.toFixed is not a function\n    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)\n    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)\n    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)\n    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)\n    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)\n    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)\n    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)\n    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)\n    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)\n    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595) {\n    \"url\": \"http:\\/\\/127.0.0.1:8000\\/wallet\\/transactions\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:47:06.084Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.427159, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.197349, "end": **********.442236, "duration": 0.2448868751525879, "duration_str": "245ms", "measures": [{"label": "Booting", "start": **********.197349, "relative_start": 0, "end": **********.403257, "relative_end": **********.403257, "duration": 0.*****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.403272, "relative_start": 0.*****************, "end": **********.442239, "relative_end": 3.0994415283203125e-06, "duration": 0.038967132568359375, "duration_str": "38.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.421789, "relative_start": 0.****************, "end": **********.423871, "relative_end": **********.423871, "duration": 0.0020821094512939453, "duration_str": "2.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.440031, "relative_start": 0.*****************, "end": **********.440389, "relative_end": **********.440389, "duration": 0.0003578662872314453, "duration_str": "358μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.44041, "relative_start": 0.*****************, "end": **********.440429, "relative_end": **********.440429, "duration": 1.9073486328125e-05, "duration_str": "19μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.436386, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "246ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:47:06.084Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Uncaught TypeError: m.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>6</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>1869</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"42 characters\">m.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"803 characters\">TypeError: m.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/wallet/transactions</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:47:06.084Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Uncaught TypeError: m.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>6</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>1869</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"42 characters\">m.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"803 characters\">TypeError: m.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at J (http://127.0.0.1:8000/build/assets/transactions-Cp-fno28.js:6:1869)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at As (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:34140)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Gs (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:62259)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Mh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:72762)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at ap (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:106784)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at T0 (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105850)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at co (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:105682)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at Wh (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:102794)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at mp (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:122:114220)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"803 characters\">    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C3firZZ8.js:99:1595)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/wallet/transactions</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2672</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/wallet/transactions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"825 characters\">_ga=GA1.1.668794480.**********; _ga_69MPZE94D5=GS2.1.s1753378233$o1$g0$t1753378235$j58$l0$h0; appearance=light; XSRF-TOKEN=eyJpdiI6ImF2cmVub05QaUZXbE9oYTIzSW0wWkE9PSIsInZhbHVlIjoic2Q2L0VPbVpGQ050SHJUMENWc3VoUHlGeTJiZnpKbkE4TUtKbHFEWENmeDg5cGdpRUYvakpMR3NDNmREM3JXbzlxWFE0U3BFbXdlYnUvRmtmb3NqSTcwK2xzRlR6djl6UVRZQXR1MitXZmFlNHRnVTF1bFRsMFpUNFhLcks1ZmYiLCJtYWMiOiI2ZGY2OTdiZTk5NTYwNGY0YmU1NDc5ZjJmNTY1Y2MzYWI3YjQ0Y2U5NzE3MTU0ZWU3N2ZkZGE4NTcwOGRmMDEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjgraFRqdDV1N0RvWlhHYmJqaWtkTlE9PSIsInZhbHVlIjoiWjZBWW9oZ1RsMFNnWDc0WlBuaHA4eXBFQTNWQVRPRzBuZU1tclhuUmtzc2Q2NVZxTUpKTHpOYkpxNHFOL0o4Zk15eVJYM3A4bllNQ0tsR1FUVWtQaWxiWFFxeTR4N1hSWEsrcWo3TUhnejA3Z3hSVjVaQVpFTVA2azFZb0dYeHMiLCJtYWMiOiI4MGVhM2NmMTFiNmI5YjQ3M2JlNmE5ODU4N2VmMDAyM2IzZWY1MDRlMDM2NTE5ZjNmMTFlYWJhYjZiOWM0OWIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => \"<span class=sf-dump-str title=\"26 characters\">GA1.1.668794480.**********</span>\"\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => \"<span class=sf-dump-str title=\"45 characters\">GS2.1.s1753378233$o1$g0$t1753378235$j58$l0$h0</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImF2cmVub05QaUZXbE9oYTIzSW0wWkE9PSIsInZhbHVlIjoic2Q2L0VPbVpGQ050SHJUMENWc3VoUHlGeTJiZnpKbkE4TUtKbHFEWENmeDg5cGdpRUYvakpMR3NDNmREM3JXbzlxWFE0U3BFbXdlYnUvRmtmb3NqSTcwK2xzRlR6djl6UVRZQXR1MitXZmFlNHRnVTF1bFRsMFpUNFhLcks1ZmYiLCJtYWMiOiI2ZGY2OTdiZTk5NTYwNGY0YmU1NDc5ZjJmNTY1Y2MzYWI3YjQ0Y2U5NzE3MTU0ZWU3N2ZkZGE4NTcwOGRmMDEzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjgraFRqdDV1N0RvWlhHYmJqaWtkTlE9PSIsInZhbHVlIjoiWjZBWW9oZ1RsMFNnWDc0WlBuaHA4eXBFQTNWQVRPRzBuZU1tclhuUmtzc2Q2NVZxTUpKTHpOYkpxNHFOL0o4Zk15eVJYM3A4bllNQ0tsR1FUVWtQaWxiWFFxeTR4N1hSWEsrcWo3TUhnejA3Z3hSVjVaQVpFTVA2azFZb0dYeHMiLCJtYWMiOiI4MGVhM2NmMTFiNmI5YjQ3M2JlNmE5ODU4N2VmMDAyM2IzZWY1MDRlMDM2NTE5ZjNmMTFlYWJhYjZiOWM0OWIwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-292700774 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:47:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292700774\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1494649960 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1494649960\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}