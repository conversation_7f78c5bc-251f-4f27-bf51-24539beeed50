{"__meta": {"id": "01K409ECRWXVXXDAZ33S9ACHVH", "datetime": "2025-08-31 14:45:33", "utime": **********.085096, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[14:45:33] LOG.error: Uncaught TypeError: d.user.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js 6 1866 TypeError d.user.wallet_balance.toFixed is not a function TypeError: d.user.wallet_balance.toFixed is not a function\n    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)\n    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)\n    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)\n    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)\n    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)\n    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)\n    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)\n    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)\n    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)\n    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595) {\n    \"url\": \"http:\\/\\/127.0.0.1:8000\\/wallet\\/add-funds\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:45:32.812Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.074798, "xdebug_link": null, "collector": "log"}, {"message": "[14:45:33] LOG.error: Uncaught TypeError: d.user.wallet_balance.toFixed is not a function http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js 6 1866 TypeError d.user.wallet_balance.toFixed is not a function TypeError: d.user.wallet_balance.toFixed is not a function\n    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)\n    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)\n    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)\n    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)\n    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)\n    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)\n    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)\n    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)\n    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)\n    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595) {\n    \"url\": \"http:\\/\\/127.0.0.1:8000\\/wallet\\/add-funds\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-08-31T14:45:32.813Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.074972, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": 1756651532.942301, "end": **********.085112, "duration": 0.14281105995178223, "duration_str": "143ms", "measures": [{"label": "Booting", "start": 1756651532.942301, "relative_start": 0, "end": **********.05737, "relative_end": **********.05737, "duration": 0.*****************, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.057378, "relative_start": 0.*****************, "end": **********.085113, "relative_end": 9.5367431640625e-07, "duration": 0.027734994888305664, "duration_str": "27.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.070103, "relative_start": 0.*****************, "end": **********.071504, "relative_end": **********.071504, "duration": 0.0014011859893798828, "duration_str": "1.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.083674, "relative_start": 0.****************, "end": **********.083902, "relative_end": **********.083902, "duration": 0.00022792816162109375, "duration_str": "228μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.083917, "relative_start": 0.****************, "end": **********.08393, "relative_end": **********.08393, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 971}], "start": **********.081639, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:100-126</a>", "duration": "143ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-367169372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-367169372\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-587444296 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:45:32.812Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"67 characters\">Uncaught TypeError: d.user.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>6</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>1866</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"47 characters\">d.user.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"806 characters\">TypeError: d.user.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/wallet/add-funds</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-08-31T14:45:32.813Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"67 characters\">Uncaught TypeError: d.user.wallet_balance.toFixed is not a function</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>6</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>1866</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TypeError</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"47 characters\">d.user.wallet_balance.toFixed is not a function</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"806 characters\">TypeError: d.user.wallet_balance.toFixed is not a function<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at ae (http://127.0.0.1:8000/build/assets/add-funds-gb_FVb-t.js:6:1866)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at As (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:34140)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Gs (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:62259)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Mh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:72762)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at ap (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:106784)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at T0 (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105850)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at co (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:105682)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at Wh (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:102794)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at mp (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:122:114220)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"806 characters\">    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-B9VHN_ri.js:99:1595)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/wallet/add-funds</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587444296\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-53199195 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2686</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"825 characters\">_ga=GA1.1.*********.1753378234; _ga_69MPZE94D5=GS2.1.s1753378233$o1$g0$t1753378235$j58$l0$h0; appearance=light; XSRF-TOKEN=eyJpdiI6InZJa3A2cHQ3eXN3bzAxZGZYU0pCdmc9PSIsInZhbHVlIjoiVk1oZHYrVk02bGUxbTBITjRsR1dpUkxHdGpnRlBpMEFoS0MycGNjamM4S0UvNFRKWW1jN2N6Q20xQysva3lycXBlZ0piWGZwUktVSGdlazJhWUM3TTZxczZZVm5GRFRSUlhhSTc2Ny9QZURiOUQ2UGwvaTNmK3pxY2RtR2M3MGEiLCJtYWMiOiI0NDY4NGI1YTUyNjYzZTE2YWFmODRjNmZiZTA4MWU0NmMyOWYzYzcwMTZmM2NhZWQxYWM4NDAwMzU3ZmQ4MjM1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFDRXlCNEhRZCtKdXBFa2JKN0RVWlE9PSIsInZhbHVlIjoiY2l5RTd2aWhaczVadHlxYTIxODZyOEc3Q3lyYnpJUHNucW9LbmYrekxCbTFnR1MyRklDRVpWSjVEMSs4ZE95cEt2eXE5N1g4aGVCbWlpTVo0VXBaM05iMmMwK0FaL3ZCdjhVSTF0R0NFKzQ4aXJ4blhPUGFkUGNOWGRVRCtkeWUiLCJtYWMiOiI4MTJkNGQzMGM3Nzc1NTk3YmYxNjY5Mzg4ZDcyZmU3NWM0MjJlZDQzOTNjMGY3NGU3ZTc1NGFhNTgzYWU2ZGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53199195\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-5991885 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => \"<span class=sf-dump-str title=\"26 characters\">GA1.1.*********.1753378234</span>\"\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => \"<span class=sf-dump-str title=\"45 characters\">GS2.1.s1753378233$o1$g0$t1753378235$j58$l0$h0</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZJa3A2cHQ3eXN3bzAxZGZYU0pCdmc9PSIsInZhbHVlIjoiVk1oZHYrVk02bGUxbTBITjRsR1dpUkxHdGpnRlBpMEFoS0MycGNjamM4S0UvNFRKWW1jN2N6Q20xQysva3lycXBlZ0piWGZwUktVSGdlazJhWUM3TTZxczZZVm5GRFRSUlhhSTc2Ny9QZURiOUQ2UGwvaTNmK3pxY2RtR2M3MGEiLCJtYWMiOiI0NDY4NGI1YTUyNjYzZTE2YWFmODRjNmZiZTA4MWU0NmMyOWYzYzcwMTZmM2NhZWQxYWM4NDAwMzU3ZmQ4MjM1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkFDRXlCNEhRZCtKdXBFa2JKN0RVWlE9PSIsInZhbHVlIjoiY2l5RTd2aWhaczVadHlxYTIxODZyOEc3Q3lyYnpJUHNucW9LbmYrekxCbTFnR1MyRklDRVpWSjVEMSs4ZE95cEt2eXE5N1g4aGVCbWlpTVo0VXBaM05iMmMwK0FaL3ZCdjhVSTF0R0NFKzQ4aXJ4blhPUGFkUGNOWGRVRCtkeWUiLCJtYWMiOiI4MTJkNGQzMGM3Nzc1NTk3YmYxNjY5Mzg4ZDcyZmU3NWM0MjJlZDQzOTNjMGY3NGU3ZTc1NGFhNTgzYWU2ZGU2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5991885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1365122377 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:45:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365122377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1865518351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1865518351\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}